name: flutter_dct_compress
description: "Package nén ảnh DCT thuần Dart với hỗ trợ đa định dạng, x<PERSON> lý nền isolate, và kiểm soát tỷ lệ nén chính xác. Tương thích đa nền tảng không phụ thuộc native."
version: 0.0.1
homepage: https://github.com/your-username/flutter-dct-compress
repository: https://github.com/your-username/flutter-dct-compress
issue_tracker: https://github.com/your-username/flutter-dct-compress/issues
documentation: https://github.com/your-username/flutter-dct-compress/blob/main/doc/guides/vietnamese/huong_dan_su_dung.md

environment:
  sdk: ^3.8.1
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  image: ^4.1.7
  typed_data: ^1.3.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  test: ^1.24.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/to/asset-from-package
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/to/font-from-package
