# Comprehensive DCT Compression Test Suite

This directory contains a comprehensive test suite for the DCT compression functionality, covering multiple image types, sizes, and performance scenarios.

## Overview

The test suite provides:

1. **Multi-format testing**: Tests with JPEG, PNG, BMP, TIFF, WebP formats
2. **Multi-size testing**: From small thumbnails (100x100) to ultra-high resolution (8K+)
3. **Performance reporting**: Detailed metrics on processing time, compression ratios, memory usage
4. **Real image testing**: Uses actual photographs from the sample directory
5. **Automated reporting**: Generates comprehensive reports with analysis and recommendations

## Test Structure

```
test/comprehensive/
├── README.md                           # This file
├── comprehensive_dct_test.dart         # Main comprehensive test suite
├── size_performance_test.dart          # Size-specific performance tests
├── run_comprehensive_tests.dart        # Automated test runner
├── utils/
│   ├── test_utilities.dart            # Test utilities and image generation
│   ├── performance_reporter.dart      # Performance reporting system
│   └── real_image_tester.dart         # Real image testing framework
├── reports/                           # Generated reports (created during tests)
│   ├── comprehensive_test_report.md   # Main comprehensive report
│   ├── test_summary.md               # Summary statistics
│   ├── performance_charts.md         # Performance visualizations
│   └── test_metrics.json             # Raw metrics data
└── data/                             # Test data (created during tests)
```

## Running the Tests

### Option 1: Run All Comprehensive Tests (Recommended)

```bash
# Run the complete test suite with automated reporting
dart test test/comprehensive/run_comprehensive_tests.dart
```

This will:
- Run all test categories
- Test real images from `test/iamge_sample/`
- Generate synthetic test images for missing size categories
- Create comprehensive reports in `test/comprehensive/reports/`

### Option 2: Run Individual Test Suites

```bash
# Run main comprehensive tests
flutter test test/comprehensive/comprehensive_dct_test.dart

# Run size-specific performance tests
flutter test test/comprehensive/size_performance_test.dart
```

### Option 3: Run with Specific Flutter Test Commands

```bash
# Run with verbose output
flutter test test/comprehensive/ --reporter=expanded

# Run with coverage
flutter test test/comprehensive/ --coverage

# Run specific test groups
flutter test test/comprehensive/comprehensive_dct_test.dart --name "Multi-Format"
```

## Test Categories

### 1. Multi-Format Image Tests

Tests compression with various image formats:
- **Real Images**: Uses actual photographs from `test/iamge_sample/`
- **Format Compatibility**: Tests JPEG, PNG, BMP, TIFF, WebP support
- **Format-Specific Behavior**: Analyzes compression characteristics per format

### 2. Multi-Size Image Tests

Tests performance across different image sizes:
- **Small Images**: 100x100, 200x200 (thumbnails)
- **Medium Images**: 800x600, 1024x768 (standard resolutions)
- **Large Images**: 1920x1080, 4K (high resolution)
- **Ultra-Large Images**: 8K+ (stress testing)

### 3. Performance Analysis

Comprehensive performance metrics:
- **Processing Time**: Milliseconds per image
- **Compression Ratio**: Output size / input size
- **Processing Speed**: MB/s throughput
- **Memory Usage**: Peak memory consumption
- **Quality Metrics**: PSNR, SSIM (when available)

### 4. Quality vs Performance

Tests trade-offs between quality and performance:
- **Quality Levels**: 25, 50, 75, 85, 95
- **Compression Options**: High quality, high compression, web optimized
- **Color Spaces**: RGB vs YUV performance
- **Progressive vs Standard**: Encoding method comparison

## Generated Reports

### Main Report (`comprehensive_test_report.md`)

Contains:
- Executive summary with key metrics
- Performance by image size category
- Performance by image format
- Quality vs performance analysis
- Memory usage analysis
- Detailed statistics table
- Recommendations for optimal settings

### Summary Report (`test_summary.md`)

Contains:
- Test overview and success rates
- Performance summary statistics
- Best performing configurations
- Quick reference metrics

### Performance Charts (`performance_charts.md`)

Contains:
- Text-based charts showing performance trends
- Size vs speed comparisons
- Quality vs compression trade-offs

### JSON Metrics (`test_metrics.json`)

Contains:
- Raw test data for further analysis
- All performance metrics in structured format
- Metadata about test execution

## Sample Images

The test suite uses real images from `test/iamge_sample/`:
- Various JPEG photographs from Pexels
- Different subjects: portraits, landscapes, objects
- Range of sizes and complexities
- Real-world compression scenarios

## Test Image Patterns

For synthetic testing, the suite generates images with different patterns:
- **Gradient**: Smooth color transitions
- **Noise**: Random pixel values
- **Checkerboard**: High contrast patterns
- **Stripes**: Repeating patterns
- **Circles**: Geometric shapes
- **Mixed**: Combination of patterns

## Performance Expectations

### Typical Performance Ranges

| Image Size | Expected Time | Expected Speed |
|------------|---------------|----------------|
| 100x100    | < 100ms       | > 10 MB/s      |
| 800x600    | < 1s          | > 5 MB/s       |
| 1920x1080  | < 10s         | > 2 MB/s       |
| 4K (3840x2160) | < 30s     | > 1 MB/s       |

### Memory Usage

| Image Size | Estimated Memory |
|------------|------------------|
| 100x100    | < 1MB           |
| 800x600    | < 10MB          |
| 1920x1080  | < 50MB          |
| 4K          | < 200MB         |

## Troubleshooting

### Common Issues

1. **Out of Memory Errors**
   - Reduce test image sizes
   - Run tests individually instead of in batch
   - Increase system memory limits

2. **Test Timeouts**
   - Large images may take significant time
   - Adjust timeout values in test configuration
   - Consider skipping ultra-high resolution tests

3. **Missing Sample Images**
   - Ensure `test/iamge_sample/` directory exists
   - Add sample images in supported formats
   - Tests will generate synthetic images if samples are missing

4. **Report Generation Failures**
   - Check write permissions for `test/comprehensive/reports/`
   - Ensure sufficient disk space
   - Verify directory structure exists

### Performance Optimization

1. **For Faster Testing**
   - Reduce image sizes in test configurations
   - Limit quality levels tested
   - Skip ultra-high resolution tests

2. **For More Comprehensive Testing**
   - Add more sample images
   - Test additional quality levels
   - Include more compression options

## Extending the Test Suite

### Adding New Test Categories

1. Create new test file in `test/comprehensive/`
2. Use utilities from `utils/` directory
3. Add performance metrics to reporter
4. Update `run_comprehensive_tests.dart` to include new tests

### Adding New Image Formats

1. Update `TestImageFormat` enum in `test_utilities.dart`
2. Add format detection in `real_image_tester.dart`
3. Add sample images in new format to `test/iamge_sample/`

### Custom Performance Metrics

1. Extend `TestPerformanceMetrics` class
2. Add new metrics to `additionalMetrics` map
3. Update report generation to include new metrics

## Contributing

When adding new tests:
1. Follow existing naming conventions
2. Add appropriate documentation
3. Include performance metrics reporting
4. Test with both real and synthetic images
5. Update this README if adding new features
