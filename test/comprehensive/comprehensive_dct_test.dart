/// Comprehensive DCT compression test suite
/// 
/// Tests DCT compression functionality across multiple image types, sizes,
/// and configurations with detailed performance reporting.
library;

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dct_compress/flutter_dct_compress.dart';
import 'dart:io';
import 'dart:typed_data';
import 'utils/test_utilities.dart';
import 'utils/performance_reporter.dart';
import 'utils/real_image_tester.dart';

void main() {
  group('Comprehensive DCT Compression Tests', () {
    late PerformanceReporter reporter;
    late RealImageTester realImageTester;
    late DctCompressor compressor;

    setUpAll(() {
      reporter = PerformanceReporter();
      realImageTester = RealImageTester('test/iamge_sample');
      compressor = DctCompressor();
    });

    tearDownAll(() async {
      // Generate and save comprehensive report
      final reportPath = 'test/comprehensive/reports/comprehensive_test_report.md';
      final jsonPath = 'test/comprehensive/reports/test_metrics.json';
      
      // Ensure reports directory exists
      final reportsDir = Directory('test/comprehensive/reports');
      if (!await reportsDir.exists()) {
        await reportsDir.create(recursive: true);
      }

      await reporter.saveReport(reportPath, title: 'Comprehensive DCT Compression Test Report');
      await reporter.exportMetricsAsJson(jsonPath);
      
      print('\n' + '='*80);
      print('COMPREHENSIVE TEST REPORT GENERATED');
      print('='*80);
      print('Report saved to: $reportPath');
      print('Metrics exported to: $jsonPath');
      print('='*80);
    });

    group('Multi-Format Image Tests', () {
      test('Real image format compatibility', () async {
        print('\n--- Testing Real Images from Sample Directory ---');
        
        final results = await realImageTester.runComprehensiveTests();
        reporter.addAllMetrics(results);

        // Verify we tested multiple formats
        final testedFormats = results.map((r) => r.format).where((f) => f != null).toSet();
        expect(testedFormats.length, greaterThan(0), 
               reason: 'Should test at least one image format');

        // Verify successful tests
        final successfulTests = results.where((r) => r.success).toList();
        expect(successfulTests.length, greaterThan(0), 
               reason: 'Should have at least some successful tests');

        print('Tested ${results.length} total scenarios');
        print('Successful: ${successfulTests.length}');
        print('Failed: ${results.length - successfulTests.length}');
        print('Formats tested: ${testedFormats.map((f) => f!.label).join(', ')}');
      });

      test('Format-specific compression behavior', () async {
        print('\n--- Testing Format-Specific Behavior ---');
        
        // Test synthetic images in different formats
        for (final format in TestImageFormat.values) {
          if (format == TestImageFormat.tiff || format == TestImageFormat.webp) {
            continue; // Skip formats that might not be fully supported
          }

          final testImage = TestImageGenerator.generateTestImage(
            width: 512,
            height: 512,
            pattern: TestImagePattern.mixed,
          );

          try {
            final stopwatch = Stopwatch()..start();
            final result = await compressor.compressImage(
              testImage,
              options: CompressionOptions.quality(75),
            );
            stopwatch.stop();

            final metrics = TestPerformanceMetrics(
              testName: 'format_test_${format.label}',
              sizeCategory: ImageSizeCategory.medium,
              format: format,
              quality: 75,
              processingTime: stopwatch.elapsed,
              originalSize: testImage.length,
              compressedSize: result.compressedSize,
              compressionRatio: result.compressionRatio,
              success: result.isSuccess,
              errorMessage: result.errorMessage,
              timestamp: DateTime.now(),
              additionalMetrics: {'testType': 'format_specific'},
            );

            reporter.addMetrics(metrics);
            expect(result.isSuccess, isTrue, reason: 'Compression should succeed for ${format.label}');

          } catch (e) {
            print('Format ${format.label} test failed: $e');
            // Add failed test to metrics
            reporter.addMetrics(TestPerformanceMetrics(
              testName: 'format_test_${format.label}',
              sizeCategory: ImageSizeCategory.medium,
              format: format,
              quality: 75,
              processingTime: Duration.zero,
              originalSize: testImage.length,
              compressedSize: 0,
              compressionRatio: 0.0,
              success: false,
              errorMessage: e.toString(),
              timestamp: DateTime.now(),
            ));
          }
        }
      });
    });

    group('Multi-Size Image Tests', () {
      test('Size category performance scaling', () async {
        print('\n--- Testing Size Category Performance ---');
        
        for (final sizeCategory in ImageSizeCategory.values) {
          print('Testing ${sizeCategory.description}...');
          
          // Test multiple patterns for each size
          for (final pattern in [TestImagePattern.gradient, TestImagePattern.mixed]) {
            final testImage = TestImageGenerator.generateTestImage(
              width: sizeCategory.width,
              height: sizeCategory.height,
              pattern: pattern,
            );

            try {
              final stopwatch = Stopwatch()..start();
              final result = await compressor.compressImage(
                testImage,
                options: CompressionOptions.quality(75),
              );
              stopwatch.stop();

              final metrics = TestPerformanceMetrics(
                testName: 'size_test_${sizeCategory.label}_${pattern.name}',
                sizeCategory: sizeCategory,
                format: TestImageFormat.png,
                quality: 75,
                processingTime: stopwatch.elapsed,
                originalSize: testImage.length,
                compressedSize: result.compressedSize,
                compressionRatio: result.compressionRatio,
                success: result.isSuccess,
                errorMessage: result.errorMessage,
                timestamp: DateTime.now(),
                additionalMetrics: {
                  'pattern': pattern.name,
                  'testType': 'size_scaling',
                },
              );

              reporter.addMetrics(metrics);
              expect(result.isSuccess, isTrue, 
                     reason: 'Compression should succeed for ${sizeCategory.label}');

              print('  ${pattern.name}: ${stopwatch.elapsedMilliseconds}ms, '
                    'ratio: ${result.compressionRatio.toStringAsFixed(3)}');

            } catch (e) {
              print('  ${pattern.name}: FAILED - $e');
              reporter.addMetrics(TestPerformanceMetrics(
                testName: 'size_test_${sizeCategory.label}_${pattern.name}',
                sizeCategory: sizeCategory,
                format: TestImageFormat.png,
                quality: 75,
                processingTime: Duration.zero,
                originalSize: testImage.length,
                compressedSize: 0,
                compressionRatio: 0.0,
                success: false,
                errorMessage: e.toString(),
                timestamp: DateTime.now(),
              ));
            }
          }
        }
      });

      test('Memory usage scaling with image size', () async {
        print('\n--- Testing Memory Usage Scaling ---');
        
        final testSizes = [
          ImageSizeCategory.small,
          ImageSizeCategory.medium,
          ImageSizeCategory.large,
        ];

        for (final sizeCategory in testSizes) {
          final testImage = TestImageGenerator.generateTestImage(
            width: sizeCategory.width,
            height: sizeCategory.height,
            pattern: TestImagePattern.gradient,
          );

          try {
            // TODO: Add memory monitoring
            final stopwatch = Stopwatch()..start();
            final result = await compressor.compressImage(
              testImage,
              options: CompressionOptions.quality(75),
            );
            stopwatch.stop();

            final estimatedMemory = sizeCategory.totalPixels * 4; // Rough estimate
            
            final metrics = TestPerformanceMetrics(
              testName: 'memory_test_${sizeCategory.label}',
              sizeCategory: sizeCategory,
              format: TestImageFormat.png,
              quality: 75,
              processingTime: stopwatch.elapsed,
              originalSize: testImage.length,
              compressedSize: result.compressedSize,
              compressionRatio: result.compressionRatio,
              memoryUsed: estimatedMemory,
              success: result.isSuccess,
              timestamp: DateTime.now(),
              additionalMetrics: {'testType': 'memory_scaling'},
            );

            reporter.addMetrics(metrics);
            
            print('  ${sizeCategory.label}: ~${(estimatedMemory / (1024*1024)).toStringAsFixed(1)}MB estimated');

          } catch (e) {
            print('  ${sizeCategory.label}: FAILED - $e');
          }
        }
      });
    });

    group('Quality vs Performance Analysis', () {
      test('Quality level performance impact', () async {
        print('\n--- Testing Quality Level Impact ---');
        
        final testImage = TestImageGenerator.generateTestImage(
          width: 800,
          height: 600,
          pattern: TestImagePattern.mixed,
        );

        final qualityLevels = [25, 50, 75, 85, 95];
        
        for (final quality in qualityLevels) {
          try {
            final stopwatch = Stopwatch()..start();
            final result = await compressor.compressImage(
              testImage,
              options: CompressionOptions.quality(quality),
            );
            stopwatch.stop();

            final metrics = TestPerformanceMetrics(
              testName: 'quality_test_$quality',
              sizeCategory: ImageSizeCategory.medium,
              format: TestImageFormat.png,
              quality: quality,
              processingTime: stopwatch.elapsed,
              originalSize: testImage.length,
              compressedSize: result.compressedSize,
              compressionRatio: result.compressionRatio,
              success: result.isSuccess,
              timestamp: DateTime.now(),
              additionalMetrics: {'testType': 'quality_analysis'},
            );

            reporter.addMetrics(metrics);
            
            print('  Quality $quality: ${stopwatch.elapsedMilliseconds}ms, '
                  'ratio: ${result.compressionRatio.toStringAsFixed(3)}');

          } catch (e) {
            print('  Quality $quality: FAILED - $e');
          }
        }
      });

      test('Compression options performance comparison', () async {
        print('\n--- Testing Compression Options ---');
        
        final testImage = TestImageGenerator.generateTestImage(
          width: 512,
          height: 512,
          pattern: TestImagePattern.gradient,
        );

        final optionConfigs = [
          ('default', CompressionOptions.quality(75)),
          ('high_quality', CompressionOptions.highQuality()),
          ('high_compression', CompressionOptions.highCompression()),
          ('web_optimized', CompressionOptions.webOptimized()),
          ('rgb_colorspace', CompressionOptions(quality: 75, colorSpace: ColorSpace.rgb)),
          ('progressive', CompressionOptions(quality: 75, progressive: true)),
          ('no_adaptive', CompressionOptions(quality: 75, useAdaptiveQuantization: false)),
        ];

        for (final (name, options) in optionConfigs) {
          try {
            final stopwatch = Stopwatch()..start();
            final result = await compressor.compressImage(testImage, options: options);
            stopwatch.stop();

            final metrics = TestPerformanceMetrics(
              testName: 'options_test_$name',
              sizeCategory: ImageSizeCategory.medium,
              format: TestImageFormat.png,
              quality: options.quality,
              processingTime: stopwatch.elapsed,
              originalSize: testImage.length,
              compressedSize: result.compressedSize,
              compressionRatio: result.compressionRatio,
              success: result.isSuccess,
              timestamp: DateTime.now(),
              additionalMetrics: {
                'testType': 'options_comparison',
                'optionName': name,
                'colorSpace': options.colorSpace.name,
                'progressive': options.progressive,
                'adaptiveQuantization': options.useAdaptiveQuantization,
              },
            );

            reporter.addMetrics(metrics);
            
            print('  $name: ${stopwatch.elapsedMilliseconds}ms, '
                  'ratio: ${result.compressionRatio.toStringAsFixed(3)}');

          } catch (e) {
            print('  $name: FAILED - $e');
          }
        }
      });
    });
  });
}
