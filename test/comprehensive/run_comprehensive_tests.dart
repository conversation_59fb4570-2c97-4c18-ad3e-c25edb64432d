/// Automated test runner for comprehensive DCT compression tests
/// 
/// Executes all test suites and generates consolidated reports
/// comparing performance across all tested scenarios.
library;

import 'dart:io';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dct_compress/flutter_dct_compress.dart';
import 'utils/test_utilities.dart';
import 'utils/performance_reporter.dart';
import 'utils/real_image_tester.dart';

/// Comprehensive test runner and report generator
class ComprehensiveTestRunner {
  final PerformanceReporter _globalReporter = PerformanceReporter();
  final List<String> _testSuites = [
    'test/comprehensive/comprehensive_dct_test.dart',
    'test/comprehensive/size_performance_test.dart',
  ];

  /// Run all comprehensive tests and generate reports
  Future<void> runAllTests() async {
    print('=' * 80);
    print('STARTING COMPREHENSIVE DCT COMPRESSION TEST SUITE');
    print('=' * 80);
    
    final startTime = DateTime.now();
    
    try {
      // Ensure output directories exist
      await _ensureDirectoriesExist();
      
      // Run real image tests
      await _runRealImageTests();
      
      // Run synthetic image tests
      await _runSyntheticImageTests();
      
      // Generate comprehensive reports
      await _generateReports();
      
      final endTime = DateTime.now();
      final totalDuration = endTime.difference(startTime);
      
      print('\n' + '=' * 80);
      print('COMPREHENSIVE TEST SUITE COMPLETED');
      print('=' * 80);
      print('Total Duration: ${totalDuration.inMinutes}m ${totalDuration.inSeconds % 60}s');
      print('Reports generated in: test/comprehensive/reports/');
      print('=' * 80);
      
    } catch (e) {
      print('\nERROR: Test suite failed: $e');
      rethrow;
    }
  }

  /// Run tests with real images from sample directory
  Future<void> _runRealImageTests() async {
    print('\n--- REAL IMAGE TESTS ---');
    
    final realImageTester = RealImageTester('test/iamge_sample');
    
    try {
      final results = await realImageTester.runComprehensiveTests();
      _globalReporter.addAllMetrics(results);
      
      print('Real image tests completed: ${results.length} test scenarios');
      print('Successful: ${results.where((r) => r.success).length}');
      print('Failed: ${results.where((r) => !r.success).length}');
      
    } catch (e) {
      print('Real image tests failed: $e');
    }
  }

  /// Run synthetic image tests for comprehensive coverage
  Future<void> _runSyntheticImageTests() async {
    print('\n--- SYNTHETIC IMAGE TESTS ---');
    
    await _runSizeScalingTests();
    await _runQualityAnalysisTests();
    await _runFormatCompatibilityTests();
    await _runPerformanceStressTests();
  }

  /// Test performance scaling across different image sizes
  Future<void> _runSizeScalingTests() async {
    print('\nRunning size scaling tests...');
    
    final compressor = DctCompressor();
    
    for (final sizeCategory in ImageSizeCategory.values) {
      // Skip ultra-large for automated tests to avoid memory issues
      if (sizeCategory == ImageSizeCategory.ultraLarge) continue;
      
      try {
        final testImage = TestImageGenerator.generateTestImage(
          width: sizeCategory.width,
          height: sizeCategory.height,
          pattern: TestImagePattern.mixed,
        );

        final stopwatch = Stopwatch()..start();
        final result = await compressor.compressImage(
          testImage,
          options: CompressionOptions.quality(75),
        );
        stopwatch.stop();

        final metrics = TestPerformanceMetrics(
          testName: 'auto_size_${sizeCategory.label}',
          sizeCategory: sizeCategory,
          format: TestImageFormat.png,
          quality: 75,
          processingTime: stopwatch.elapsed,
          originalSize: testImage.length,
          compressedSize: result.compressedSize,
          compressionRatio: result.compressionRatio,
          success: result.isSuccess,
          timestamp: DateTime.now(),
          additionalMetrics: {'testType': 'automated_size_scaling'},
        );

        _globalReporter.addMetrics(metrics);
        print('  ${sizeCategory.label}: ${stopwatch.elapsedMilliseconds}ms');

      } catch (e) {
        print('  ${sizeCategory.label}: FAILED - $e');
      }
    }
  }

  /// Test quality vs performance trade-offs
  Future<void> _runQualityAnalysisTests() async {
    print('\nRunning quality analysis tests...');
    
    final compressor = DctCompressor();
    final testImage = TestImageGenerator.generateTestImage(
      width: 800,
      height: 600,
      pattern: TestImagePattern.mixed,
    );

    final qualityLevels = [10, 25, 50, 75, 85, 95];
    
    for (final quality in qualityLevels) {
      try {
        final stopwatch = Stopwatch()..start();
        final result = await compressor.compressImage(
          testImage,
          options: CompressionOptions.quality(quality),
        );
        stopwatch.stop();

        final metrics = TestPerformanceMetrics(
          testName: 'auto_quality_$quality',
          sizeCategory: ImageSizeCategory.medium,
          format: TestImageFormat.png,
          quality: quality,
          processingTime: stopwatch.elapsed,
          originalSize: testImage.length,
          compressedSize: result.compressedSize,
          compressionRatio: result.compressionRatio,
          success: result.isSuccess,
          timestamp: DateTime.now(),
          additionalMetrics: {'testType': 'automated_quality_analysis'},
        );

        _globalReporter.addMetrics(metrics);
        print('  Quality $quality: ${result.compressionRatio.toStringAsFixed(3)} ratio');

      } catch (e) {
        print('  Quality $quality: FAILED - $e');
      }
    }
  }

  /// Test format compatibility
  Future<void> _runFormatCompatibilityTests() async {
    print('\nRunning format compatibility tests...');
    
    final compressor = DctCompressor();
    
    for (final pattern in TestImagePattern.values) {
      final testImage = TestImageGenerator.generateTestImage(
        width: 512,
        height: 512,
        pattern: pattern,
      );

      try {
        final stopwatch = Stopwatch()..start();
        final result = await compressor.compressImage(
          testImage,
          options: CompressionOptions.quality(75),
        );
        stopwatch.stop();

        final metrics = TestPerformanceMetrics(
          testName: 'auto_pattern_${pattern.name}',
          sizeCategory: ImageSizeCategory.medium,
          format: TestImageFormat.png,
          quality: 75,
          processingTime: stopwatch.elapsed,
          originalSize: testImage.length,
          compressedSize: result.compressedSize,
          compressionRatio: result.compressionRatio,
          success: result.isSuccess,
          timestamp: DateTime.now(),
          additionalMetrics: {
            'testType': 'automated_pattern_test',
            'pattern': pattern.name,
          },
        );

        _globalReporter.addMetrics(metrics);
        print('  Pattern ${pattern.name}: ${result.compressionRatio.toStringAsFixed(3)} ratio');

      } catch (e) {
        print('  Pattern ${pattern.name}: FAILED - $e');
      }
    }
  }

  /// Run performance stress tests
  Future<void> _runPerformanceStressTests() async {
    print('\nRunning performance stress tests...');
    
    final compressor = DctCompressor();
    
    // Batch processing test
    final batchSizes = [5, 10, 20];
    
    for (final batchSize in batchSizes) {
      try {
        final images = <Uint8List>[];
        for (int i = 0; i < batchSize; i++) {
          images.add(TestImageGenerator.generateTestImage(
            width: 256,
            height: 256,
            pattern: TestImagePattern.values[i % TestImagePattern.values.length],
            seed: i,
          ));
        }

        final stopwatch = Stopwatch()..start();
        var successCount = 0;
        var totalOriginalSize = 0;
        var totalCompressedSize = 0;

        for (final image in images) {
          try {
            final result = await compressor.compressImage(
              image,
              options: CompressionOptions.quality(75),
            );
            
            if (result.isSuccess) {
              successCount++;
              totalOriginalSize += image.length;
              totalCompressedSize += result.compressedSize;
            }
          } catch (e) {
            // Continue with other images
          }
        }
        
        stopwatch.stop();

        final metrics = TestPerformanceMetrics(
          testName: 'auto_batch_$batchSize',
          sizeCategory: ImageSizeCategory.medium,
          format: TestImageFormat.png,
          quality: 75,
          processingTime: stopwatch.elapsed,
          originalSize: totalOriginalSize,
          compressedSize: totalCompressedSize,
          compressionRatio: successCount > 0 ? totalCompressedSize / totalOriginalSize : 0.0,
          success: successCount == batchSize,
          timestamp: DateTime.now(),
          additionalMetrics: {
            'testType': 'automated_batch_test',
            'batchSize': batchSize,
            'successCount': successCount,
            'throughput': batchSize / (stopwatch.elapsedMilliseconds / 1000.0),
          },
        );

        _globalReporter.addMetrics(metrics);
        print('  Batch $batchSize: ${successCount}/$batchSize successful, '
              '${(batchSize / (stopwatch.elapsedMilliseconds / 1000.0)).toStringAsFixed(1)} images/sec');

      } catch (e) {
        print('  Batch $batchSize: FAILED - $e');
      }
    }
  }

  /// Generate comprehensive reports
  Future<void> _generateReports() async {
    print('\n--- GENERATING REPORTS ---');
    
    // Main comprehensive report
    final mainReportPath = 'test/comprehensive/reports/comprehensive_test_report.md';
    await _globalReporter.saveReport(
      mainReportPath,
      title: 'Comprehensive DCT Compression Test Report',
    );
    print('Main report: $mainReportPath');

    // JSON export for data analysis
    final jsonPath = 'test/comprehensive/reports/test_metrics.json';
    await _globalReporter.exportMetricsAsJson(jsonPath);
    print('JSON metrics: $jsonPath');

    // Generate summary statistics
    await _generateSummaryReport();
    
    // Generate performance comparison charts (text-based)
    await _generatePerformanceCharts();
  }

  /// Generate summary statistics report
  Future<void> _generateSummaryReport() async {
    final summaryPath = 'test/comprehensive/reports/test_summary.md';
    final file = File(summaryPath);
    
    final buffer = StringBuffer();
    buffer.writeln('# DCT Compression Test Summary');
    buffer.writeln('Generated: ${DateTime.now().toIso8601String()}');
    buffer.writeln();

    // Get all metrics for analysis
    final allMetrics = _globalReporter.metrics;
    final successfulTests = allMetrics.where((m) => m.success).toList();
    
    buffer.writeln('## Test Overview');
    buffer.writeln('- **Total Tests**: ${allMetrics.length}');
    buffer.writeln('- **Successful**: ${successfulTests.length}');
    buffer.writeln('- **Failed**: ${allMetrics.length - successfulTests.length}');
    buffer.writeln('- **Success Rate**: ${(successfulTests.length / allMetrics.length * 100).toStringAsFixed(1)}%');
    buffer.writeln();

    if (successfulTests.isNotEmpty) {
      // Performance statistics
      final avgTime = successfulTests.map((m) => m.processingTime.inMilliseconds).reduce((a, b) => a + b) / successfulTests.length;
      final avgRatio = successfulTests.map((m) => m.compressionRatio).reduce((a, b) => a + b) / successfulTests.length;
      final avgSpeed = successfulTests.map((m) => m.processingSpeedMBps).reduce((a, b) => a + b) / successfulTests.length;

      buffer.writeln('## Performance Summary');
      buffer.writeln('- **Average Processing Time**: ${avgTime.toStringAsFixed(1)}ms');
      buffer.writeln('- **Average Compression Ratio**: ${avgRatio.toStringAsFixed(3)}');
      buffer.writeln('- **Average Processing Speed**: ${avgSpeed.toStringAsFixed(2)} MB/s');
      buffer.writeln();

      // Best performers
      final fastestTest = successfulTests.reduce((a, b) => a.processingSpeedMBps > b.processingSpeedMBps ? a : b);
      final bestCompressionTest = successfulTests.reduce((a, b) => a.compressionRatio < b.compressionRatio ? a : b);

      buffer.writeln('## Best Performers');
      buffer.writeln('- **Fastest**: ${fastestTest.testName} (${fastestTest.processingSpeedMBps.toStringAsFixed(2)} MB/s)');
      buffer.writeln('- **Best Compression**: ${bestCompressionTest.testName} (${bestCompressionTest.compressionRatio.toStringAsFixed(3)} ratio)');
      buffer.writeln();
    }

    await file.writeAsString(buffer.toString());
    print('Summary report: $summaryPath');
  }

  /// Generate text-based performance charts
  Future<void> _generatePerformanceCharts() async {
    final chartsPath = 'test/comprehensive/reports/performance_charts.md';
    final file = File(chartsPath);
    
    final buffer = StringBuffer();
    buffer.writeln('# Performance Charts');
    buffer.writeln('Generated: ${DateTime.now().toIso8601String()}');
    buffer.writeln();

    // Size vs Performance chart
    buffer.writeln('## Processing Speed by Image Size');
    buffer.writeln('```');
    
    final allMetrics = _globalReporter.metrics;
    final successfulTests = allMetrics.where((m) => m.success).toList();
    
    final sizeGroups = <ImageSizeCategory, List<TestPerformanceMetrics>>{};
    for (final metric in successfulTests) {
      sizeGroups.putIfAbsent(metric.sizeCategory, () => []).add(metric);
    }

    for (final category in ImageSizeCategory.values) {
      final tests = sizeGroups[category];
      if (tests != null && tests.isNotEmpty) {
        final avgSpeed = tests.map((t) => t.processingSpeedMBps).reduce((a, b) => a + b) / tests.length;
        final bar = '█' * (avgSpeed * 10).round().clamp(0, 50);
        buffer.writeln('${category.label.padRight(15)} │$bar ${avgSpeed.toStringAsFixed(2)} MB/s');
      }
    }
    
    buffer.writeln('```');
    buffer.writeln();

    await file.writeAsString(buffer.toString());
    print('Performance charts: $chartsPath');
  }

  /// Ensure required directories exist
  Future<void> _ensureDirectoriesExist() async {
    final directories = [
      'test/comprehensive/reports',
      'test/comprehensive/data',
    ];

    for (final dir in directories) {
      final directory = Directory(dir);
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
    }
  }
}

/// Main entry point for running comprehensive tests
void main() async {
  final runner = ComprehensiveTestRunner();
  await runner.runAllTests();
}
