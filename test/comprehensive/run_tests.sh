#!/bin/bash

# Comprehensive DCT Compression Test Runner
# This script runs the complete test suite and generates reports

echo "=========================================="
echo "DCT Compression Comprehensive Test Suite"
echo "=========================================="

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    echo "Error: Please run this script from the project root directory"
    exit 1
fi

# Check if sample images exist
if [ ! -d "test/iamge_sample" ]; then
    echo "Warning: Sample image directory 'test/iamge_sample' not found"
    echo "Tests will run with synthetic images only"
fi

# Create reports directory if it doesn't exist
mkdir -p test/comprehensive/reports

echo ""
echo "Running comprehensive DCT compression tests..."
echo ""

# Run the main comprehensive test
echo "1. Running main comprehensive tests..."
flutter test test/comprehensive/comprehensive_dct_test.dart --reporter=expanded

if [ $? -ne 0 ]; then
    echo "Warning: Main comprehensive tests had failures"
fi

echo ""
echo "2. Running size performance tests..."
flutter test test/comprehensive/size_performance_test.dart --reporter=expanded

if [ $? -ne 0 ]; then
    echo "Warning: Size performance tests had failures"
fi

echo ""
echo "3. Running automated test suite with reporting..."
dart test/comprehensive/run_comprehensive_tests.dart

if [ $? -ne 0 ]; then
    echo "Warning: Automated test suite had issues"
fi

echo ""
echo "=========================================="
echo "Test Suite Completed"
echo "=========================================="

# Check if reports were generated
if [ -d "test/comprehensive/reports" ]; then
    echo "Reports generated in test/comprehensive/reports/:"
    ls -la test/comprehensive/reports/
    echo ""
    echo "Main report: test/comprehensive/reports/comprehensive_test_report.md"
    echo "Summary: test/comprehensive/reports/test_summary.md"
    echo "JSON data: test/comprehensive/reports/test_metrics.json"
else
    echo "Warning: Reports directory not found"
fi

echo ""
echo "To view the main report:"
echo "cat test/comprehensive/reports/comprehensive_test_report.md"
echo ""
echo "To analyze JSON data:"
echo "cat test/comprehensive/reports/test_metrics.json | jq ."
