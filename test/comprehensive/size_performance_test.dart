/// Size-specific performance tests for DCT compression
/// 
/// Focuses on testing performance across different image sizes
/// from small thumbnails to ultra-high resolution images.
library;

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dct_compress/flutter_dct_compress.dart';
import 'dart:typed_data';
import 'dart:math' as math;
import 'utils/test_utilities.dart';
import 'utils/performance_reporter.dart';

void main() {
  group('Size Performance Tests', () {
    late DctCompressor compressor;
    late PerformanceReporter reporter;

    setUpAll(() {
      compressor = DctCompressor();
      reporter = PerformanceReporter();
    });

    tearDownAll(() async {
      // Save size-specific performance report
      final reportPath = 'test/comprehensive/reports/size_performance_report.md';
      await reporter.saveReport(reportPath, title: 'DCT Compression Size Performance Report');
      print('Size performance report saved to: $reportPath');
    });

    group('Small Image Performance', () {
      test('Thumbnail size optimization (100x100)', () async {
        print('\n--- Testing Thumbnail Sizes ---');
        
        final sizes = [
          (64, 64),
          (100, 100),
          (128, 128),
          (200, 200),
        ];

        for (final (width, height) in sizes) {
          for (final pattern in TestImagePattern.values) {
            final testImage = TestImageGenerator.generateTestImage(
              width: width,
              height: height,
              pattern: pattern,
            );

            final stopwatch = Stopwatch()..start();
            final result = await compressor.compressImage(
              testImage,
              options: CompressionOptions.quality(85),
            );
            stopwatch.stop();

            final sizeCategory = ImageSizeCategory.values.firstWhere(
              (cat) => cat.width >= width && cat.height >= height,
              orElse: () => ImageSizeCategory.small,
            );

            final metrics = TestPerformanceMetrics(
              testName: 'thumbnail_${width}x${height}_${pattern.name}',
              sizeCategory: sizeCategory,
              format: TestImageFormat.png,
              quality: 85,
              processingTime: stopwatch.elapsed,
              originalSize: testImage.length,
              compressedSize: result.compressedSize,
              compressionRatio: result.compressionRatio,
              success: result.isSuccess,
              timestamp: DateTime.now(),
              additionalMetrics: {
                'actualWidth': width,
                'actualHeight': height,
                'pattern': pattern.name,
                'testType': 'thumbnail',
              },
            );

            reporter.addMetrics(metrics);

            print('  ${width}x$height ${pattern.name}: ${stopwatch.elapsedMilliseconds}ms');
            
            // Small images should process very quickly
            expect(stopwatch.elapsedMilliseconds, lessThan(1000), 
                   reason: 'Small images should process in under 1 second');
          }
        }
      });

      test('Small image batch processing efficiency', () async {
        print('\n--- Testing Small Image Batch Processing ---');
        
        final batchSizes = [10, 25, 50, 100];
        
        for (final batchSize in batchSizes) {
          final images = <Uint8List>[];
          
          // Generate batch of small images
          for (int i = 0; i < batchSize; i++) {
            images.add(TestImageGenerator.generateTestImage(
              width: 150,
              height: 150,
              pattern: TestImagePattern.values[i % TestImagePattern.values.length],
              seed: i,
            ));
          }

          final stopwatch = Stopwatch()..start();
          
          // Process batch sequentially (simulating batch processing)
          var totalOriginalSize = 0;
          var totalCompressedSize = 0;
          var successCount = 0;
          
          for (final image in images) {
            try {
              final result = await compressor.compressImage(
                image,
                options: CompressionOptions.quality(75),
              );
              
              if (result.isSuccess) {
                totalOriginalSize += image.length;
                totalCompressedSize += result.compressedSize;
                successCount++;
              }
            } catch (e) {
              // Continue with other images
            }
          }
          
          stopwatch.stop();

          final avgCompressionRatio = successCount > 0 
              ? totalCompressedSize / totalOriginalSize 
              : 0.0;

          final metrics = TestPerformanceMetrics(
            testName: 'small_batch_$batchSize',
            sizeCategory: ImageSizeCategory.small,
            format: TestImageFormat.png,
            quality: 75,
            processingTime: stopwatch.elapsed,
            originalSize: totalOriginalSize,
            compressedSize: totalCompressedSize,
            compressionRatio: avgCompressionRatio,
            success: successCount == batchSize,
            timestamp: DateTime.now(),
            additionalMetrics: {
              'batchSize': batchSize,
              'successCount': successCount,
              'testType': 'small_batch',
              'imagesPerSecond': batchSize / (stopwatch.elapsedMilliseconds / 1000.0),
            },
          );

          reporter.addMetrics(metrics);

          print('  Batch $batchSize: ${stopwatch.elapsedMilliseconds}ms, '
                '${(batchSize / (stopwatch.elapsedMilliseconds / 1000.0)).toStringAsFixed(1)} images/sec');
        }
      });
    });

    group('Medium Image Performance', () {
      test('Standard resolution performance (800x600, 1024x768)', () async {
        print('\n--- Testing Standard Resolutions ---');
        
        final standardSizes = [
          (640, 480, 'VGA'),
          (800, 600, 'SVGA'),
          (1024, 768, 'XGA'),
          (1280, 720, 'HD'),
          (1366, 768, 'WXGA'),
        ];

        for (final (width, height, name) in standardSizes) {
          final testImage = TestImageGenerator.generateTestImage(
            width: width,
            height: height,
            pattern: TestImagePattern.mixed,
          );

          final stopwatch = Stopwatch()..start();
          final result = await compressor.compressImage(
            testImage,
            options: CompressionOptions.quality(80),
          );
          stopwatch.stop();

          final sizeCategory = ImageSizeCategory.values.firstWhere(
            (cat) => cat.totalPixels >= width * height,
            orElse: () => ImageSizeCategory.medium,
          );

          final metrics = TestPerformanceMetrics(
            testName: 'standard_${name}_${width}x$height',
            sizeCategory: sizeCategory,
            format: TestImageFormat.png,
            quality: 80,
            processingTime: stopwatch.elapsed,
            originalSize: testImage.length,
            compressedSize: result.compressedSize,
            compressionRatio: result.compressionRatio,
            success: result.isSuccess,
            timestamp: DateTime.now(),
            additionalMetrics: {
              'resolutionName': name,
              'actualWidth': width,
              'actualHeight': height,
              'testType': 'standard_resolution',
            },
          );

          reporter.addMetrics(metrics);

          print('  $name (${width}x$height): ${stopwatch.elapsedMilliseconds}ms, '
                '${metrics.processingSpeedMBps.toStringAsFixed(2)} MB/s');

          // Medium images should process reasonably quickly
          expect(stopwatch.elapsedSeconds, lessThan(30), 
                 reason: 'Medium images should process in under 30 seconds');
        }
      });
    });

    group('Large Image Performance', () {
      test('High resolution performance (1920x1080, 4K)', () async {
        print('\n--- Testing High Resolutions ---');
        
        final highResSizes = [
          (1920, 1080, 'Full HD'),
          (2560, 1440, '1440p'),
          (3840, 2160, '4K UHD'),
        ];

        for (final (width, height, name) in highResSizes) {
          print('  Testing $name (${width}x$height)...');
          
          // Use simpler patterns for large images to reduce memory usage
          final testImage = TestImageGenerator.generateTestImage(
            width: width,
            height: height,
            pattern: TestImagePattern.gradient,
          );

          try {
            final stopwatch = Stopwatch()..start();
            final result = await compressor.compressImage(
              testImage,
              options: CompressionOptions.quality(75),
            );
            stopwatch.stop();

            final sizeCategory = ImageSizeCategory.values.firstWhere(
              (cat) => cat.totalPixels >= width * height,
              orElse: () => ImageSizeCategory.large,
            );

            final metrics = TestPerformanceMetrics(
              testName: 'high_res_${name.replaceAll(' ', '_')}_${width}x$height',
              sizeCategory: sizeCategory,
              format: TestImageFormat.png,
              quality: 75,
              processingTime: stopwatch.elapsed,
              originalSize: testImage.length,
              compressedSize: result.compressedSize,
              compressionRatio: result.compressionRatio,
              success: result.isSuccess,
              timestamp: DateTime.now(),
              additionalMetrics: {
                'resolutionName': name,
                'actualWidth': width,
                'actualHeight': height,
                'testType': 'high_resolution',
              },
            );

            reporter.addMetrics(metrics);

            print('    Success: ${stopwatch.elapsedMilliseconds}ms, '
                  '${metrics.processingSpeedMBps.toStringAsFixed(2)} MB/s');

            // Large images may take longer but should still complete
            expect(result.isSuccess, isTrue, reason: 'Large image compression should succeed');

          } catch (e) {
            print('    Failed: $e');
            
            // Record failure
            reporter.addMetrics(TestPerformanceMetrics(
              testName: 'high_res_${name.replaceAll(' ', '_')}_${width}x$height',
              sizeCategory: ImageSizeCategory.large,
              format: TestImageFormat.png,
              quality: 75,
              processingTime: Duration.zero,
              originalSize: testImage.length,
              compressedSize: 0,
              compressionRatio: 0.0,
              success: false,
              errorMessage: e.toString(),
              timestamp: DateTime.now(),
            ));
          }
        }
      });

      test('Ultra-high resolution stress test (8K+)', () async {
        print('\n--- Testing Ultra-High Resolutions ---');
        
        final ultraHighResSizes = [
          (7680, 4320, '8K UHD'),
          // Note: Larger sizes might cause memory issues, so we'll be careful
        ];

        for (final (width, height, name) in ultraHighResSizes) {
          print('  Testing $name (${width}x$height)...');
          
          try {
            // Use the simplest pattern to minimize memory usage
            final testImage = TestImageGenerator.generateTestImage(
              width: width,
              height: height,
              pattern: TestImagePattern.gradient,
            );

            final stopwatch = Stopwatch()..start();
            final result = await compressor.compressImage(
              testImage,
              options: CompressionOptions(
                quality: 60, // Lower quality for ultra-high res
                useIsolate: true, // Ensure isolate usage for large images
              ),
            );
            stopwatch.stop();

            final metrics = TestPerformanceMetrics(
              testName: 'ultra_high_res_${name.replaceAll(' ', '_')}_${width}x$height',
              sizeCategory: ImageSizeCategory.ultraLarge,
              format: TestImageFormat.png,
              quality: 60,
              processingTime: stopwatch.elapsed,
              originalSize: testImage.length,
              compressedSize: result.compressedSize,
              compressionRatio: result.compressionRatio,
              success: result.isSuccess,
              timestamp: DateTime.now(),
              additionalMetrics: {
                'resolutionName': name,
                'actualWidth': width,
                'actualHeight': height,
                'testType': 'ultra_high_resolution',
              },
            );

            reporter.addMetrics(metrics);

            print('    Success: ${stopwatch.elapsedSeconds}s, '
                  '${metrics.processingSpeedMBps.toStringAsFixed(2)} MB/s');

            // Ultra-high res images are expected to take significant time
            expect(result.isSuccess, isTrue, reason: 'Ultra-high res compression should succeed');

          } catch (e) {
            print('    Failed (expected for memory constraints): $e');
            
            // Record failure - this might be expected due to memory constraints
            reporter.addMetrics(TestPerformanceMetrics(
              testName: 'ultra_high_res_${name.replaceAll(' ', '_')}_${width}x$height',
              sizeCategory: ImageSizeCategory.ultraLarge,
              format: TestImageFormat.png,
              quality: 60,
              processingTime: Duration.zero,
              originalSize: 0,
              compressedSize: 0,
              compressionRatio: 0.0,
              success: false,
              errorMessage: e.toString(),
              timestamp: DateTime.now(),
            ));
          }
        }
      });
    });

    group('Size Scaling Analysis', () {
      test('Performance scaling with image size', () async {
        print('\n--- Analyzing Performance Scaling ---');
        
        final scalingSizes = [
          (100, 100),
          (200, 200),
          (400, 400),
          (800, 800),
          (1600, 1600),
        ];

        final scalingResults = <String, TestPerformanceMetrics>[];

        for (final (width, height) in scalingSizes) {
          final testImage = TestImageGenerator.generateTestImage(
            width: width,
            height: height,
            pattern: TestImagePattern.gradient,
          );

          try {
            final stopwatch = Stopwatch()..start();
            final result = await compressor.compressImage(
              testImage,
              options: CompressionOptions.quality(75),
            );
            stopwatch.stop();

            final sizeCategory = ImageSizeCategory.values.firstWhere(
              (cat) => cat.totalPixels >= width * height,
              orElse: () => ImageSizeCategory.large,
            );

            final metrics = TestPerformanceMetrics(
              testName: 'scaling_${width}x$height',
              sizeCategory: sizeCategory,
              format: TestImageFormat.png,
              quality: 75,
              processingTime: stopwatch.elapsed,
              originalSize: testImage.length,
              compressedSize: result.compressedSize,
              compressionRatio: result.compressionRatio,
              success: result.isSuccess,
              timestamp: DateTime.now(),
              additionalMetrics: {
                'actualWidth': width,
                'actualHeight': height,
                'totalPixels': width * height,
                'testType': 'scaling_analysis',
              },
            );

            reporter.addMetrics(metrics);
            scalingResults['${width}x$height'] = metrics;

            print('  ${width}x$height: ${stopwatch.elapsedMilliseconds}ms, '
                  '${metrics.processingSpeedMBps.toStringAsFixed(2)} MB/s');

          } catch (e) {
            print('  ${width}x$height: FAILED - $e');
          }
        }

        // Analyze scaling behavior
        if (scalingResults.length >= 2) {
          print('\n  Scaling Analysis:');
          final sortedResults = scalingResults.entries.toList()
            ..sort((a, b) => a.value.originalSize.compareTo(b.value.originalSize));
          
          for (int i = 1; i < sortedResults.length; i++) {
            final prev = sortedResults[i-1].value;
            final curr = sortedResults[i].value;
            
            final sizeRatio = curr.originalSize / prev.originalSize;
            final timeRatio = curr.processingTime.inMicroseconds / prev.processingTime.inMicroseconds;
            
            print('    ${prev.additionalMetrics['actualWidth']}x${prev.additionalMetrics['actualHeight']} → '
                  '${curr.additionalMetrics['actualWidth']}x${curr.additionalMetrics['actualHeight']}: '
                  '${sizeRatio.toStringAsFixed(1)}x size, ${timeRatio.toStringAsFixed(1)}x time');
          }
        }
      });
    });
  });
}
