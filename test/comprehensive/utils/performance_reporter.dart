/// Performance reporting system for DCT compression tests
/// 
/// Generates comprehensive reports with statistics, charts, and analysis
/// of DCT compression performance across different scenarios.
library;

import 'dart:io';
import 'dart:convert';
import 'dart:math' as math;
import 'test_utilities.dart';

/// Comprehensive performance report generator
class PerformanceReporter {
  final List<TestPerformanceMetrics> _metrics = [];
  final DateTime _reportStartTime = DateTime.now();

  /// Add test metrics to the report
  void addMetrics(TestPerformanceMetrics metrics) {
    _metrics.add(metrics);
  }

  /// Add multiple metrics
  void addAllMetrics(List<TestPerformanceMetrics> metricsList) {
    _metrics.addAll(metricsList);
  }

  /// Get all metrics
  List<TestPerformanceMetrics> get metrics => List.from(_metrics);

  /// Generate comprehensive report
  Future<String> generateReport({
    String title = 'DCT Compression Performance Report',
    bool includeDetailedStats = true,
    bool includeCharts = true,
  }) async {
    final buffer = StringBuffer();
    
    // Header
    buffer.writeln('# $title');
    buffer.writeln('Generated: ${DateTime.now().toIso8601String()}');
    buffer.writeln('Test Duration: ${DateTime.now().difference(_reportStartTime)}');
    buffer.writeln('Total Tests: ${_metrics.length}');
    buffer.writeln('Successful Tests: ${_metrics.where((m) => m.success).length}');
    buffer.writeln('Failed Tests: ${_metrics.where((m) => !m.success).length}');
    buffer.writeln();

    // Executive Summary
    buffer.writeln('## Executive Summary');
    buffer.writeln(_generateExecutiveSummary());
    buffer.writeln();

    // Performance by Size Category
    buffer.writeln('## Performance by Image Size');
    buffer.writeln(_generateSizeAnalysis());
    buffer.writeln();

    // Performance by Format
    buffer.writeln('## Performance by Image Format');
    buffer.writeln(_generateFormatAnalysis());
    buffer.writeln();

    // Quality Analysis
    buffer.writeln('## Quality vs Performance Analysis');
    buffer.writeln(_generateQualityAnalysis());
    buffer.writeln();

    // Memory Usage Analysis
    buffer.writeln('## Memory Usage Analysis');
    buffer.writeln(_generateMemoryAnalysis());
    buffer.writeln();

    if (includeDetailedStats) {
      // Detailed Statistics
      buffer.writeln('## Detailed Statistics');
      buffer.writeln(_generateDetailedStats());
      buffer.writeln();
    }

    // Recommendations
    buffer.writeln('## Recommendations');
    buffer.writeln(_generateRecommendations());
    buffer.writeln();

    // Failed Tests (if any)
    final failedTests = _metrics.where((m) => !m.success).toList();
    if (failedTests.isNotEmpty) {
      buffer.writeln('## Failed Tests');
      for (final test in failedTests) {
        buffer.writeln('- ${test.testName}: ${test.errorMessage}');
      }
      buffer.writeln();
    }

    return buffer.toString();
  }

  String _generateExecutiveSummary() {
    final successfulTests = _metrics.where((m) => m.success).toList();
    if (successfulTests.isEmpty) return 'No successful tests to analyze.';

    final avgProcessingTime = _calculateAverage(successfulTests.map((m) => m.processingTime.inMilliseconds.toDouble()));
    final avgCompressionRatio = _calculateAverage(successfulTests.map((m) => m.compressionRatio));
    final avgSpeedMBps = _calculateAverage(successfulTests.map((m) => m.processingSpeedMBps));
    final avgSpaceSavings = _calculateAverage(successfulTests.map((m) => m.spaceSavingsPercent));

    final buffer = StringBuffer();
    buffer.writeln('- **Average Processing Time**: ${avgProcessingTime.toStringAsFixed(1)}ms');
    buffer.writeln('- **Average Compression Ratio**: ${avgCompressionRatio.toStringAsFixed(3)}');
    buffer.writeln('- **Average Processing Speed**: ${avgSpeedMBps.toStringAsFixed(2)} MB/s');
    buffer.writeln('- **Average Space Savings**: ${avgSpaceSavings.toStringAsFixed(1)}%');
    
    // Best and worst performers
    final fastestTest = successfulTests.reduce((a, b) => a.processingSpeedMBps > b.processingSpeedMBps ? a : b);
    final slowestTest = successfulTests.reduce((a, b) => a.processingSpeedMBps < b.processingSpeedMBps ? a : b);
    
    buffer.writeln('- **Fastest Test**: ${fastestTest.testName} (${fastestTest.processingSpeedMBps.toStringAsFixed(2)} MB/s)');
    buffer.writeln('- **Slowest Test**: ${slowestTest.testName} (${slowestTest.processingSpeedMBps.toStringAsFixed(2)} MB/s)');

    return buffer.toString();
  }

  String _generateSizeAnalysis() {
    final buffer = StringBuffer();
    
    for (final category in ImageSizeCategory.values) {
      final categoryTests = _metrics.where((m) => m.sizeCategory == category && m.success).toList();
      if (categoryTests.isEmpty) continue;

      final avgTime = _calculateAverage(categoryTests.map((m) => m.processingTime.inMilliseconds.toDouble()));
      final avgRatio = _calculateAverage(categoryTests.map((m) => m.compressionRatio));
      final avgSpeed = _calculateAverage(categoryTests.map((m) => m.processingSpeedMBps));

      buffer.writeln('### ${category.description}');
      buffer.writeln('- Tests: ${categoryTests.length}');
      buffer.writeln('- Avg Time: ${avgTime.toStringAsFixed(1)}ms');
      buffer.writeln('- Avg Compression: ${avgRatio.toStringAsFixed(3)}');
      buffer.writeln('- Avg Speed: ${avgSpeed.toStringAsFixed(2)} MB/s');
      buffer.writeln();
    }

    return buffer.toString();
  }

  String _generateFormatAnalysis() {
    final buffer = StringBuffer();
    
    for (final format in TestImageFormat.values) {
      final formatTests = _metrics.where((m) => m.format == format && m.success).toList();
      if (formatTests.isEmpty) continue;

      final avgTime = _calculateAverage(formatTests.map((m) => m.processingTime.inMilliseconds.toDouble()));
      final avgRatio = _calculateAverage(formatTests.map((m) => m.compressionRatio));
      final avgSpeed = _calculateAverage(formatTests.map((m) => m.processingSpeedMBps));

      buffer.writeln('### ${format.label}');
      buffer.writeln('- Tests: ${formatTests.length}');
      buffer.writeln('- Avg Time: ${avgTime.toStringAsFixed(1)}ms');
      buffer.writeln('- Avg Compression: ${avgRatio.toStringAsFixed(3)}');
      buffer.writeln('- Avg Speed: ${avgSpeed.toStringAsFixed(2)} MB/s');
      buffer.writeln();
    }

    return buffer.toString();
  }

  String _generateQualityAnalysis() {
    final buffer = StringBuffer();
    final qualityGroups = <int, List<TestPerformanceMetrics>>{};
    
    for (final metric in _metrics.where((m) => m.success)) {
      qualityGroups.putIfAbsent(metric.quality, () => []).add(metric);
    }

    for (final quality in qualityGroups.keys.toList()..sort()) {
      final tests = qualityGroups[quality]!;
      final avgTime = _calculateAverage(tests.map((m) => m.processingTime.inMilliseconds.toDouble()));
      final avgRatio = _calculateAverage(tests.map((m) => m.compressionRatio));
      final avgSpeed = _calculateAverage(tests.map((m) => m.processingSpeedMBps));

      buffer.writeln('### Quality $quality');
      buffer.writeln('- Tests: ${tests.length}');
      buffer.writeln('- Avg Time: ${avgTime.toStringAsFixed(1)}ms');
      buffer.writeln('- Avg Compression: ${avgRatio.toStringAsFixed(3)}');
      buffer.writeln('- Avg Speed: ${avgSpeed.toStringAsFixed(2)} MB/s');
      buffer.writeln();
    }

    return buffer.toString();
  }

  String _generateMemoryAnalysis() {
    final testsWithMemory = _metrics.where((m) => m.success && m.memoryUsed != null).toList();
    if (testsWithMemory.isEmpty) {
      return 'No memory usage data available.';
    }

    final buffer = StringBuffer();
    final avgMemory = _calculateAverage(testsWithMemory.map((m) => m.memoryUsed!.toDouble()));
    final maxMemory = testsWithMemory.map((m) => m.memoryUsed!).reduce(math.max);
    final minMemory = testsWithMemory.map((m) => m.memoryUsed!).reduce(math.min);

    buffer.writeln('- **Average Memory Usage**: ${_formatBytes(avgMemory.round())}');
    buffer.writeln('- **Peak Memory Usage**: ${_formatBytes(maxMemory)}');
    buffer.writeln('- **Minimum Memory Usage**: ${_formatBytes(minMemory)}');

    // Memory efficiency by size
    buffer.writeln('\n#### Memory Efficiency by Size:');
    for (final category in ImageSizeCategory.values) {
      final categoryTests = testsWithMemory.where((m) => m.sizeCategory == category).toList();
      if (categoryTests.isEmpty) continue;

      final avgMemoryForSize = _calculateAverage(categoryTests.map((m) => m.memoryUsed!.toDouble()));
      final memoryPerPixel = avgMemoryForSize / category.totalPixels;
      
      buffer.writeln('- ${category.label}: ${_formatBytes(avgMemoryForSize.round())} '
                    '(${memoryPerPixel.toStringAsFixed(2)} bytes/pixel)');
    }

    return buffer.toString();
  }

  String _generateDetailedStats() {
    final buffer = StringBuffer();
    
    buffer.writeln('| Test Name | Size | Format | Quality | Time (ms) | Ratio | Speed (MB/s) | Memory |');
    buffer.writeln('|-----------|------|--------|---------|-----------|-------|--------------|--------|');
    
    for (final metric in _metrics) {
      if (!metric.success) continue;
      
      buffer.writeln('| ${metric.testName} | '
                    '${metric.sizeCategory.label} | '
                    '${metric.format?.label ?? 'N/A'} | '
                    '${metric.quality} | '
                    '${metric.processingTime.inMilliseconds} | '
                    '${metric.compressionRatio.toStringAsFixed(3)} | '
                    '${metric.processingSpeedMBps.toStringAsFixed(2)} | '
                    '${metric.memoryUsed != null ? _formatBytes(metric.memoryUsed!) : 'N/A'} |');
    }

    return buffer.toString();
  }

  String _generateRecommendations() {
    final buffer = StringBuffer();
    final successfulTests = _metrics.where((m) => m.success).toList();
    
    if (successfulTests.isEmpty) {
      return 'No successful tests to generate recommendations from.';
    }

    // Find optimal quality settings
    final qualityGroups = <int, List<TestPerformanceMetrics>>{};
    for (final metric in successfulTests) {
      qualityGroups.putIfAbsent(metric.quality, () => []).add(metric);
    }

    var bestQuality = 85;
    var bestScore = 0.0;
    
    for (final entry in qualityGroups.entries) {
      final quality = entry.key;
      final tests = entry.value;
      final avgSpeed = _calculateAverage(tests.map((m) => m.processingSpeedMBps));
      final avgCompression = _calculateAverage(tests.map((m) => 1.0 - m.compressionRatio)); // Higher is better
      
      // Simple scoring: balance speed and compression
      final score = avgSpeed * 0.3 + avgCompression * 100 * 0.7;
      if (score > bestScore) {
        bestScore = score;
        bestQuality = quality;
      }
    }

    buffer.writeln('### Optimal Settings');
    buffer.writeln('- **Recommended Quality**: $bestQuality (best balance of speed and compression)');

    // Best performing size categories
    final sizePerformance = <ImageSizeCategory, double>{};
    for (final category in ImageSizeCategory.values) {
      final categoryTests = successfulTests.where((m) => m.sizeCategory == category).toList();
      if (categoryTests.isNotEmpty) {
        sizePerformance[category] = _calculateAverage(categoryTests.map((m) => m.processingSpeedMBps));
      }
    }

    if (sizePerformance.isNotEmpty) {
      final bestSize = sizePerformance.entries.reduce((a, b) => a.value > b.value ? a : b);
      buffer.writeln('- **Most Efficient Size**: ${bestSize.key.label} (${bestSize.value.toStringAsFixed(2)} MB/s)');
    }

    // Performance warnings
    buffer.writeln('\n### Performance Considerations');
    final slowTests = successfulTests.where((m) => m.processingSpeedMBps < 1.0).toList();
    if (slowTests.isNotEmpty) {
      buffer.writeln('- **Warning**: ${slowTests.length} tests showed processing speed < 1 MB/s');
      buffer.writeln('  Consider optimizing for: ${slowTests.map((t) => t.sizeCategory.label).toSet().join(', ')}');
    }

    final highMemoryTests = successfulTests.where((m) => m.memoryUsed != null && m.memoryUsed! > 100 * 1024 * 1024).toList();
    if (highMemoryTests.isNotEmpty) {
      buffer.writeln('- **Memory Warning**: ${highMemoryTests.length} tests used > 100MB memory');
    }

    return buffer.toString();
  }

  /// Save report to file
  Future<void> saveReport(String filePath, {String title = 'DCT Compression Performance Report'}) async {
    final report = await generateReport(title: title);
    final file = File(filePath);
    await file.writeAsString(report);
  }

  /// Export metrics as JSON
  Future<void> exportMetricsAsJson(String filePath) async {
    final jsonData = {
      'reportMetadata': {
        'generatedAt': DateTime.now().toIso8601String(),
        'testStartTime': _reportStartTime.toIso8601String(),
        'totalTests': _metrics.length,
        'successfulTests': _metrics.where((m) => m.success).length,
      },
      'metrics': _metrics.map((m) => m.toMap()).toList(),
    };

    final file = File(filePath);
    await file.writeAsString(jsonEncode(jsonData));
  }

  /// Helper methods
  double _calculateAverage(Iterable<double> values) {
    if (values.isEmpty) return 0.0;
    return values.reduce((a, b) => a + b) / values.length;
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
}
