/// Real image testing framework for DCT compression
/// 
/// <PERSON>les testing with actual photographs and real-world images
/// from the sample directory and generates additional test images.
library;

import 'dart:io';
import 'dart:typed_data';
import 'dart:math' as math;
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dct_compress/flutter_dct_compress.dart';
import 'package:image/image.dart' as img;
import 'test_utilities.dart';
import 'performance_reporter.dart';

/// Real image test configuration
class RealImageTestConfig {
  final String imagePath;
  final String name;
  final TestImageFormat originalFormat;
  final List<int> qualityLevels;
  final List<CompressionOptions> compressionOptions;
  final bool generateVariants;

  const RealImageTestConfig({
    required this.imagePath,
    required this.name,
    required this.originalFormat,
    this.qualityLevels = const [25, 50, 75, 85, 95],
    this.compressionOptions = const [],
    this.generateVariants = true,
  });
}

/// Real image testing framework
class RealImageTester {
  final DctCompressor _compressor = DctCompressor();
  final List<TestPerformanceMetrics> _results = [];
  final String _sampleDirectory;

  RealImageTester(this._sampleDirectory);

  /// Discover all sample images in the directory
  Future<List<RealImageTestConfig>> discoverSampleImages() async {
    final directory = Directory(_sampleDirectory);
    if (!await directory.exists()) {
      throw Exception('Sample directory does not exist: $_sampleDirectory');
    }

    final configs = <RealImageTestConfig>[];
    await for (final entity in directory.list()) {
      if (entity is File) {
        final extension = entity.path.split('.').last.toLowerCase();
        final format = _getFormatFromExtension(extension);
        if (format != null) {
          final name = entity.path.split('/').last.split('.').first;
          configs.add(RealImageTestConfig(
            imagePath: entity.path,
            name: name,
            originalFormat: format,
          ));
        }
      }
    }

    return configs;
  }

  /// Run comprehensive tests on all discovered images
  Future<List<TestPerformanceMetrics>> runComprehensiveTests() async {
    _results.clear();
    
    print('Discovering sample images...');
    final configs = await discoverSampleImages();
    print('Found ${configs.length} sample images');

    for (final config in configs) {
      print('\nTesting image: ${config.name}');
      await _testSingleImage(config);
    }

    // Generate synthetic test images for missing size categories
    await _generateAndTestSyntheticImages();

    return List.from(_results);
  }

  /// Test a single real image with various configurations
  Future<void> _testSingleImage(RealImageTestConfig config) async {
    try {
      // Load and analyze the image
      final imageFile = File(config.imagePath);
      final imageBytes = await imageFile.readAsBytes();
      final image = img.decodeImage(imageBytes);
      
      if (image == null) {
        print('  Failed to decode image: ${config.imagePath}');
        return;
      }

      final sizeCategory = _categorizeSizeFromImage(image);
      print('  Size: ${image.width}x${image.height} (${sizeCategory.label})');

      // Test with different quality levels
      for (final quality in config.qualityLevels) {
        await _testImageWithQuality(
          config,
          imageBytes,
          sizeCategory,
          quality,
        );
      }

      // Test with different compression options
      final additionalOptions = [
        CompressionOptions.highQuality(),
        CompressionOptions.highCompression(),
        CompressionOptions.webOptimized(),
        CompressionOptions(quality: 75, colorSpace: ColorSpace.rgb),
        CompressionOptions(quality: 75, colorSpace: ColorSpace.yuv),
        CompressionOptions(quality: 75, progressive: true),
        CompressionOptions(quality: 75, useAdaptiveQuantization: false),
      ];

      for (final options in additionalOptions) {
        await _testImageWithOptions(
          config,
          imageBytes,
          sizeCategory,
          options,
        );
      }

      // Generate size variants if requested
      if (config.generateVariants) {
        await _generateAndTestVariants(config, image, sizeCategory);
      }

    } catch (e) {
      print('  Error testing ${config.name}: $e');
      _results.add(TestPerformanceMetrics(
        testName: '${config.name}_error',
        sizeCategory: ImageSizeCategory.medium,
        format: config.originalFormat,
        quality: 0,
        processingTime: Duration.zero,
        originalSize: 0,
        compressedSize: 0,
        compressionRatio: 0.0,
        success: false,
        errorMessage: e.toString(),
        timestamp: DateTime.now(),
      ));
    }
  }

  /// Test image with specific quality setting
  Future<void> _testImageWithQuality(
    RealImageTestConfig config,
    Uint8List imageBytes,
    ImageSizeCategory sizeCategory,
    int quality,
  ) async {
    final testName = '${config.name}_q${quality}';
    
    try {
      final stopwatch = Stopwatch()..start();
      final result = await _compressor.compressImage(
        imageBytes,
        options: CompressionOptions.quality(quality),
      );
      stopwatch.stop();

      _results.add(TestPerformanceMetrics(
        testName: testName,
        sizeCategory: sizeCategory,
        format: config.originalFormat,
        quality: quality,
        processingTime: stopwatch.elapsed,
        originalSize: imageBytes.length,
        compressedSize: result.compressedSize,
        compressionRatio: result.compressionRatio,
        memoryUsed: null, // TODO: Add memory tracking
        psnr: result.psnr,
        ssim: result.ssim,
        success: result.isSuccess,
        errorMessage: result.errorMessage,
        timestamp: DateTime.now(),
        additionalMetrics: {
          'originalFormat': config.originalFormat.label,
          'usedAdaptiveQuantization': result.usedAdaptiveQuantization,
          'processedBlocks': result.processedBlocks,
        },
      ));

      print('    Quality $quality: ${stopwatch.elapsedMilliseconds}ms, '
            'ratio: ${result.compressionRatio.toStringAsFixed(3)}');

    } catch (e) {
      print('    Quality $quality: FAILED - $e');
      _results.add(TestPerformanceMetrics(
        testName: testName,
        sizeCategory: sizeCategory,
        format: config.originalFormat,
        quality: quality,
        processingTime: Duration.zero,
        originalSize: imageBytes.length,
        compressedSize: 0,
        compressionRatio: 0.0,
        success: false,
        errorMessage: e.toString(),
        timestamp: DateTime.now(),
      ));
    }
  }

  /// Test image with specific compression options
  Future<void> _testImageWithOptions(
    RealImageTestConfig config,
    Uint8List imageBytes,
    ImageSizeCategory sizeCategory,
    CompressionOptions options,
  ) async {
    final testName = '${config.name}_${_getOptionsDescription(options)}';
    
    try {
      final stopwatch = Stopwatch()..start();
      final result = await _compressor.compressImage(imageBytes, options: options);
      stopwatch.stop();

      _results.add(TestPerformanceMetrics(
        testName: testName,
        sizeCategory: sizeCategory,
        format: config.originalFormat,
        quality: options.quality,
        processingTime: stopwatch.elapsed,
        originalSize: imageBytes.length,
        compressedSize: result.compressedSize,
        compressionRatio: result.compressionRatio,
        success: result.isSuccess,
        errorMessage: result.errorMessage,
        timestamp: DateTime.now(),
        additionalMetrics: {
          'colorSpace': options.colorSpace.name,
          'progressive': options.progressive,
          'adaptiveQuantization': options.useAdaptiveQuantization,
        },
      ));

    } catch (e) {
      _results.add(TestPerformanceMetrics(
        testName: testName,
        sizeCategory: sizeCategory,
        format: config.originalFormat,
        quality: options.quality,
        processingTime: Duration.zero,
        originalSize: imageBytes.length,
        compressedSize: 0,
        compressionRatio: 0.0,
        success: false,
        errorMessage: e.toString(),
        timestamp: DateTime.now(),
      ));
    }
  }

  /// Generate and test size variants of an image
  Future<void> _generateAndTestVariants(
    RealImageTestConfig config,
    img.Image originalImage,
    ImageSizeCategory originalCategory,
  ) async {
    final variantSizes = [
      ImageSizeCategory.small,
      ImageSizeCategory.medium,
      ImageSizeCategory.large,
    ].where((size) => size != originalCategory).toList();

    for (final targetSize in variantSizes) {
      try {
        // Resize image
        final resized = img.copyResize(
          originalImage,
          width: targetSize.width,
          height: targetSize.height,
        );

        final resizedBytes = Uint8List.fromList(img.encodeJpeg(resized, quality: 95));
        
        // Test the resized variant
        await _testImageWithQuality(
          RealImageTestConfig(
            imagePath: '${config.imagePath}_resized_${targetSize.label}',
            name: '${config.name}_${targetSize.label}',
            originalFormat: TestImageFormat.jpeg,
          ),
          resizedBytes,
          targetSize,
          75, // Standard quality for variants
        );

      } catch (e) {
        print('    Failed to generate ${targetSize.label} variant: $e');
      }
    }
  }

  /// Generate and test synthetic images for missing size categories
  Future<void> _generateAndTestSyntheticImages() async {
    print('\nGenerating synthetic test images...');
    
    final testedSizes = _results.map((r) => r.sizeCategory).toSet();
    final missingSizes = ImageSizeCategory.values.where((size) => !testedSizes.contains(size)).toList();

    for (final sizeCategory in missingSizes) {
      print('  Generating ${sizeCategory.label} synthetic images...');
      
      for (final pattern in TestImagePattern.values) {
        final imageBytes = TestImageGenerator.generateTestImage(
          width: sizeCategory.width,
          height: sizeCategory.height,
          pattern: pattern,
        );

        final testName = 'synthetic_${sizeCategory.label}_${pattern.name}';
        
        try {
          final stopwatch = Stopwatch()..start();
          final result = await _compressor.compressImage(
            imageBytes,
            options: CompressionOptions.quality(75),
          );
          stopwatch.stop();

          _results.add(TestPerformanceMetrics(
            testName: testName,
            sizeCategory: sizeCategory,
            format: TestImageFormat.png, // Generated as PNG
            quality: 75,
            processingTime: stopwatch.elapsed,
            originalSize: imageBytes.length,
            compressedSize: result.compressedSize,
            compressionRatio: result.compressionRatio,
            success: result.isSuccess,
            errorMessage: result.errorMessage,
            timestamp: DateTime.now(),
            additionalMetrics: {
              'synthetic': true,
              'pattern': pattern.name,
            },
          ));

        } catch (e) {
          print('    Failed to test $testName: $e');
        }
      }
    }
  }

  /// Helper methods
  TestImageFormat? _getFormatFromExtension(String extension) {
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return TestImageFormat.jpeg;
      case 'png':
        return TestImageFormat.png;
      case 'bmp':
        return TestImageFormat.bmp;
      case 'tiff':
      case 'tif':
        return TestImageFormat.tiff;
      case 'webp':
        return TestImageFormat.webp;
      default:
        return null;
    }
  }

  ImageSizeCategory _categorizeSizeFromImage(img.Image image) {
    final totalPixels = image.width * image.height;
    
    for (final category in ImageSizeCategory.values.reversed) {
      if (totalPixels >= category.totalPixels * 0.8) { // 80% threshold
        return category;
      }
    }
    
    return ImageSizeCategory.small;
  }

  String _getOptionsDescription(CompressionOptions options) {
    final parts = <String>[];
    
    if (options.colorSpace != ColorSpace.yuv) {
      parts.add(options.colorSpace.name);
    }
    
    if (options.progressive) {
      parts.add('progressive');
    }
    
    if (!options.useAdaptiveQuantization) {
      parts.add('noAdaptive');
    }
    
    if (options.quality != 85) {
      parts.add('q${options.quality}');
    }

    return parts.isEmpty ? 'default' : parts.join('_');
  }

  /// Get all test results
  List<TestPerformanceMetrics> getResults() => List.from(_results);

  /// Clear all results
  void clearResults() => _results.clear();
}
