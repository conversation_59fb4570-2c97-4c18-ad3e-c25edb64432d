/// Comprehensive test utilities for DCT compression testing
/// 
/// Provides utilities for test image generation, performance measurement,
/// and report generation for comprehensive DCT compression testing.
library;

import 'dart:io';
import 'dart:typed_data';
import 'dart:math' as math;
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dct_compress/flutter_dct_compress.dart';
import 'package:image/image.dart' as img;

/// Test image size categories
enum ImageSizeCategory {
  small('Small', 100, 100),
  smallMedium('Small-Medium', 200, 200),
  medium('Medium', 800, 600),
  mediumLarge('Medium-Large', 1024, 768),
  large('Large', 1920, 1080),
  veryLarge('Very Large', 3840, 2160), // 4K
  ultraLarge('Ultra Large', 7680, 4320); // 8K

  const ImageSizeCategory(this.label, this.width, this.height);
  final String label;
  final int width;
  final int height;
  
  int get totalPixels => width * height;
  double get megaPixels => totalPixels / 1000000.0;
  String get description => '$label (${width}x$height, ${megaPixels.toStringAsFixed(1)}MP)';
}

/// Test image format types
enum TestImageFormat {
  jpeg('JPEG', 'jpg'),
  png('PNG', 'png'),
  bmp('BMP', 'bmp'),
  tiff('TIFF', 'tiff'),
  webp('WebP', 'webp');

  const TestImageFormat(this.label, this.extension);
  final String label;
  final String extension;
}

/// Performance metrics for a single test
class TestPerformanceMetrics {
  final String testName;
  final ImageSizeCategory sizeCategory;
  final TestImageFormat? format;
  final int quality;
  final Duration processingTime;
  final int originalSize;
  final int compressedSize;
  final double compressionRatio;
  final int? memoryUsed;
  final double? psnr;
  final double? ssim;
  final bool success;
  final String? errorMessage;
  final DateTime timestamp;
  final Map<String, dynamic> additionalMetrics;

  const TestPerformanceMetrics({
    required this.testName,
    required this.sizeCategory,
    this.format,
    required this.quality,
    required this.processingTime,
    required this.originalSize,
    required this.compressedSize,
    required this.compressionRatio,
    this.memoryUsed,
    this.psnr,
    this.ssim,
    required this.success,
    this.errorMessage,
    required this.timestamp,
    this.additionalMetrics = const {},
  });

  /// Processing speed in MB/s
  double get processingSpeedMBps {
    if (processingTime.inMicroseconds == 0) return 0.0;
    final sizeInMB = originalSize / (1024 * 1024);
    final timeInSeconds = processingTime.inMicroseconds / 1000000.0;
    return sizeInMB / timeInSeconds;
  }

  /// Space savings percentage
  double get spaceSavingsPercent => (1.0 - compressionRatio) * 100.0;

  /// Convert to map for serialization
  Map<String, dynamic> toMap() {
    return {
      'testName': testName,
      'sizeCategory': sizeCategory.label,
      'format': format?.label,
      'quality': quality,
      'processingTimeMs': processingTime.inMilliseconds,
      'originalSize': originalSize,
      'compressedSize': compressedSize,
      'compressionRatio': compressionRatio,
      'memoryUsed': memoryUsed,
      'psnr': psnr,
      'ssim': ssim,
      'success': success,
      'errorMessage': errorMessage,
      'timestamp': timestamp.toIso8601String(),
      'processingSpeedMBps': processingSpeedMBps,
      'spaceSavingsPercent': spaceSavingsPercent,
      ...additionalMetrics,
    };
  }

  @override
  String toString() {
    if (!success) {
      return 'FAILED: $testName - $errorMessage';
    }
    return '$testName: ${sizeCategory.description}, '
           'Quality: $quality, '
           'Time: ${processingTime.inMilliseconds}ms, '
           'Ratio: ${compressionRatio.toStringAsFixed(3)}, '
           'Speed: ${processingSpeedMBps.toStringAsFixed(2)} MB/s';
  }
}

/// Test image generator for creating synthetic test images
class TestImageGenerator {
  static math.Random _random = math.Random(42); // Fixed seed for reproducibility

  /// Generate a test image with specified parameters
  static Uint8List generateTestImage({
    required int width,
    required int height,
    int channels = 3,
    TestImagePattern pattern = TestImagePattern.gradient,
    int? seed,
  }) {
    if (seed != null) {
      _random = math.Random(seed);
    }

    final image = img.Image(width: width, height: height);
    
    switch (pattern) {
      case TestImagePattern.gradient:
        _fillGradient(image);
        break;
      case TestImagePattern.noise:
        _fillNoise(image);
        break;
      case TestImagePattern.checkerboard:
        _fillCheckerboard(image);
        break;
      case TestImagePattern.stripes:
        _fillStripes(image);
        break;
      case TestImagePattern.circles:
        _fillCircles(image);
        break;
      case TestImagePattern.mixed:
        _fillMixed(image);
        break;
    }

    return Uint8List.fromList(img.encodePng(image));
  }

  static void _fillGradient(img.Image image) {
    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final r = (x * 255 / image.width).round();
        final g = (y * 255 / image.height).round();
        final b = ((x + y) * 255 / (image.width + image.height)).round();
        image.setPixelRgb(x, y, r, g, b);
      }
    }
  }

  static void _fillNoise(img.Image image) {
    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final r = _random.nextInt(256);
        final g = _random.nextInt(256);
        final b = _random.nextInt(256);
        image.setPixelRgb(x, y, r, g, b);
      }
    }
  }

  static void _fillCheckerboard(img.Image image) {
    const blockSize = 32;
    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final isBlack = ((x ~/ blockSize) + (y ~/ blockSize)) % 2 == 0;
        final color = isBlack ? 0 : 255;
        image.setPixelRgb(x, y, color, color, color);
      }
    }
  }

  static void _fillStripes(img.Image image) {
    const stripeWidth = 16;
    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final stripeIndex = (x ~/ stripeWidth) % 3;
        switch (stripeIndex) {
          case 0:
            image.setPixelRgb(x, y, 255, 0, 0); // Red
            break;
          case 1:
            image.setPixelRgb(x, y, 0, 255, 0); // Green
            break;
          case 2:
            image.setPixelRgb(x, y, 0, 0, 255); // Blue
            break;
        }
      }
    }
  }

  static void _fillCircles(img.Image image) {
    final centerX = image.width / 2;
    final centerY = image.height / 2;
    final maxRadius = math.min(centerX, centerY);
    
    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final dx = x - centerX;
        final dy = y - centerY;
        final distance = math.sqrt(dx * dx + dy * dy);
        final intensity = ((math.sin(distance / maxRadius * math.pi * 4) + 1) * 127.5).round();
        image.setPixelRgb(x, y, intensity, intensity, intensity);
      }
    }
  }

  static void _fillMixed(img.Image image) {
    // Combine multiple patterns
    final quarterWidth = image.width ~/ 2;
    final quarterHeight = image.height ~/ 2;
    
    // Top-left: gradient
    final topLeft = img.copyCrop(image, x: 0, y: 0, width: quarterWidth, height: quarterHeight);
    _fillGradient(topLeft);
    
    // Top-right: checkerboard
    final topRight = img.copyCrop(image, x: quarterWidth, y: 0, width: quarterWidth, height: quarterHeight);
    _fillCheckerboard(topRight);
    
    // Bottom-left: stripes
    final bottomLeft = img.copyCrop(image, x: 0, y: quarterHeight, width: quarterWidth, height: quarterHeight);
    _fillStripes(bottomLeft);
    
    // Bottom-right: circles
    final bottomRight = img.copyCrop(image, x: quarterWidth, y: quarterHeight, width: quarterWidth, height: quarterHeight);
    _fillCircles(bottomRight);
  }
}

/// Test image patterns
enum TestImagePattern {
  gradient,
  noise,
  checkerboard,
  stripes,
  circles,
  mixed,
}

/// Performance measurement utilities
class PerformanceMeasurement {
  static final Stopwatch _stopwatch = Stopwatch();
  static final Map<String, Duration> _measurements = {};

  /// Start measuring performance for a named operation
  static void start(String operationName) {
    _stopwatch.reset();
    _stopwatch.start();
  }

  /// Stop measuring and record the result
  static Duration stop(String operationName) {
    _stopwatch.stop();
    final duration = _stopwatch.elapsed;
    _measurements[operationName] = duration;
    return duration;
  }

  /// Get all measurements
  static Map<String, Duration> getAllMeasurements() {
    return Map.from(_measurements);
  }

  /// Clear all measurements
  static void clearMeasurements() {
    _measurements.clear();
  }

  /// Measure a function execution
  static Future<T> measure<T>(String operationName, Future<T> Function() operation) async {
    start(operationName);
    try {
      final result = await operation();
      stop(operationName);
      return result;
    } catch (e) {
      stop(operationName);
      rethrow;
    }
  }
}
