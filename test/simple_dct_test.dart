/// Simple DCT test without Flutter dependencies
/// 
/// Tests core DCT functionality without requiring Flutter framework.
library;

import 'package:test/test.dart';
import '../lib/src/dct/dct_transform.dart';
import '../lib/src/dct/quantization.dart';

void main() {
  group('DCT Transform Tests', () {
    test('Forward DCT with constant block', () {
      // Test block với giá trị constant (centered around 0)
      final constantBlock = List.generate(8, (_) => 
        List.generate(8, (_) => 0.0) // All zeros
      );
      
      final dctResult = DctTransform.forwardDct(constantBlock);
      
      // DC coefficient should be 0 for zero block
      expect(dctResult[0][0], closeTo(0.0, 0.001));
      
      // AC coefficients should be 0 for constant block
      for (int i = 0; i < 8; i++) {
        for (int j = 0; j < 8; j++) {
          if (i != 0 || j != 0) {
            expect(dctResult[i][j], closeTo(0.0, 0.001));
          }
        }
      }
    });
    
    test('Forward DCT with non-zero block', () {
      // Test block với giá trị khác 0
      final testBlock = List.generate(8, (i) => 
        List.generate(8, (j) => (i + j).toDouble())
      );
      
      final dctResult = DctTransform.forwardDct(testBlock);
      
      // DC coefficient should be non-zero
      expect(dctResult[0][0], isNot(closeTo(0.0, 0.001)));
      
      // Should have some non-zero AC coefficients
      bool hasNonZeroAC = false;
      for (int i = 0; i < 8; i++) {
        for (int j = 0; j < 8; j++) {
          if ((i != 0 || j != 0) && dctResult[i][j].abs() > 0.001) {
            hasNonZeroAC = true;
            break;
          }
        }
      }
      expect(hasNonZeroAC, isTrue);
    });
    
    test('Inverse DCT reconstructs original block', () {
      final originalBlock = [
        [10.0, 20.0, 30.0, 40.0, 50.0, 60.0, 70.0, 80.0],
        [15.0, 25.0, 35.0, 45.0, 55.0, 65.0, 75.0, 85.0],
        [20.0, 30.0, 40.0, 50.0, 60.0, 70.0, 80.0, 90.0],
        [25.0, 35.0, 45.0, 55.0, 65.0, 75.0, 85.0, 95.0],
        [30.0, 40.0, 50.0, 60.0, 70.0, 80.0, 90.0, 100.0],
        [35.0, 45.0, 55.0, 65.0, 75.0, 85.0, 95.0, 105.0],
        [40.0, 50.0, 60.0, 70.0, 80.0, 90.0, 100.0, 110.0],
        [45.0, 55.0, 65.0, 75.0, 85.0, 95.0, 105.0, 115.0],
      ];
      
      final dctResult = DctTransform.forwardDct(originalBlock);
      final reconstructed = DctTransform.inverseDct(dctResult);
      
      // Check reconstruction accuracy
      for (int i = 0; i < 8; i++) {
        for (int j = 0; j < 8; j++) {
          expect(reconstructed[i][j], closeTo(originalBlock[i][j], 1.0));
        }
      }
    });
    
    test('Fast DCT produces similar results to standard DCT', () {
      final testBlock = [
        [100.0, 110.0, 120.0, 130.0, 140.0, 150.0, 160.0, 170.0],
        [105.0, 115.0, 125.0, 135.0, 145.0, 155.0, 165.0, 175.0],
        [110.0, 120.0, 130.0, 140.0, 150.0, 160.0, 170.0, 180.0],
        [115.0, 125.0, 135.0, 145.0, 155.0, 165.0, 175.0, 185.0],
        [120.0, 130.0, 140.0, 150.0, 160.0, 170.0, 180.0, 190.0],
        [125.0, 135.0, 145.0, 155.0, 165.0, 175.0, 185.0, 195.0],
        [130.0, 140.0, 150.0, 160.0, 170.0, 180.0, 190.0, 200.0],
        [135.0, 145.0, 155.0, 165.0, 175.0, 185.0, 195.0, 205.0],
      ];
      
      final standardDct = DctTransform.forwardDct(testBlock);
      final fastDct = DctTransform.fastForwardDct(testBlock);
      
      // Compare results (should be very similar)
      double maxDiff = 0.0;
      for (int i = 0; i < 8; i++) {
        for (int j = 0; j < 8; j++) {
          final diff = (standardDct[i][j] - fastDct[i][j]).abs();
          if (diff > maxDiff) maxDiff = diff;
        }
      }
      
      // Difference should be minimal
      expect(maxDiff, lessThan(0.1));
    });
  });
  
  group('Quantization Tests', () {
    test('Standard quantization tables are valid', () {
      final lumTable = Quantization.standardLuminanceTable;
      final chromTable = Quantization.standardChrominanceTable;
      
      // Check dimensions
      expect(lumTable.length, equals(8));
      expect(lumTable[0].length, equals(8));
      expect(chromTable.length, equals(8));
      expect(chromTable[0].length, equals(8));
      
      // Check that all values are positive
      for (int i = 0; i < 8; i++) {
        for (int j = 0; j < 8; j++) {
          expect(lumTable[i][j], greaterThan(0));
          expect(chromTable[i][j], greaterThan(0));
        }
      }
    });
    
    test('Quality-based quantization table creation', () {
      final highQuality = Quantization.createQuantizationTable(95);
      final lowQuality = Quantization.createQuantizationTable(10);
      
      // High quality should have smaller quantization values
      expect(highQuality[0][0], lessThan(lowQuality[0][0]));
      expect(highQuality[7][7], lessThan(lowQuality[7][7]));
    });
    
    test('Quantization and dequantization', () {
      final dctBlock = [
        [100.5, 50.2, 25.1, 12.5, 6.2, 3.1, 1.5, 0.7],
        [75.3, 37.6, 18.8, 9.4, 4.7, 2.3, 1.1, 0.5],
        [50.1, 25.0, 12.5, 6.2, 3.1, 1.5, 0.7, 0.3],
        [25.0, 12.5, 6.2, 3.1, 1.5, 0.7, 0.3, 0.1],
        [12.5, 6.2, 3.1, 1.5, 0.7, 0.3, 0.1, 0.0],
        [6.2, 3.1, 1.5, 0.7, 0.3, 0.1, 0.0, 0.0],
        [3.1, 1.5, 0.7, 0.3, 0.1, 0.0, 0.0, 0.0],
        [1.5, 0.7, 0.3, 0.1, 0.0, 0.0, 0.0, 0.0],
      ];
      
      final quantTable = Quantization.createQuantizationTable(75);
      final quantized = Quantization.quantize(dctBlock, quantTable);
      final dequantized = Quantization.dequantize(quantized, quantTable);
      
      // Check that quantized values are integers
      for (int i = 0; i < 8; i++) {
        for (int j = 0; j < 8; j++) {
          expect(quantized[i][j], isA<int>());
        }
      }
      
      // DC coefficient should be preserved reasonably well
      expect(dequantized[0][0], closeTo(dctBlock[0][0], 10.0));
    });
  });
}
